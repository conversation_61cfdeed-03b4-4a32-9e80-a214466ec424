<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合同协议方字段测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .input-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        textarea {
            width: 100%;
            height: 100px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
        }
        button {
            background-color: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #337ecc;
        }
        .result {
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>合同协议方字段交互测试</h1>
        
        <div class="test-section">
            <div class="test-title">测试1：JSON格式转换为内部格式</div>
            <div class="input-group">
                <label>输入contractPartyJson（JSON字符串）：</label>
                <textarea id="jsonInput" placeholder='{"乙方":"邦道科技有限公司","甲方":"新耀能源科技有限公司","丙方":"浙江纽伦轴承有限公司"}'></textarea>
            </div>
            <button onclick="testJsonToParties()">转换为内部格式</button>
            <div id="jsonResult" class="result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">测试2：内部格式转换为JSON</div>
            <div class="input-group">
                <label>输入内部格式数据（JSON数组）：</label>
                <textarea id="partiesInput" placeholder='[{"label":"甲方","value":"新耀能源科技有限公司","field":"partA"},{"label":"乙方","value":"邦道科技有限公司","field":"partB"}]'></textarea>
            </div>
            <button onclick="testPartiesToJson()">转换为JSON格式</button>
            <div id="partiesResult" class="result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">测试3：完整流程测试</div>
            <div class="input-group">
                <label>原始contractPartyJson：</label>
                <textarea id="fullTestInput" placeholder='{"乙方":"邦道科技有限公司","甲方":"新耀能源科技有限公司","丙方":"浙江纽伦轴承有限公司"}'></textarea>
            </div>
            <button onclick="testFullFlow()">完整流程测试</button>
            <div id="fullTestResult" class="result"></div>
        </div>
    </div>

    <script>
        // 天干顺序映射
        const tianGanMap = {
            "甲方": { index: 0, field: "partA" },
            "乙方": { index: 1, field: "partB" },
            "丙方": { index: 2, field: "partC" },
            "丁方": { index: 3, field: "partD" },
            "戊方": { index: 4, field: "partE" },
            "己方": { index: 5, field: "partF" },
            "庚方": { index: 6, field: "partG" },
            "辛方": { index: 7, field: "partH" },
            "壬方": { index: 8, field: "partI" },
            "癸方": { index: 9, field: "partJ" }
        };

        // 将JSON格式的协议方数据转换为组件内部格式
        function convertJsonToParties(partyJson) {
            const parties = [];

            // 按照天干顺序排序并转换
            Object.keys(partyJson)
                .sort((a, b) => {
                    const indexA = tianGanMap[a]?.index ?? 999;
                    const indexB = tianGanMap[b]?.index ?? 999;
                    return indexA - indexB;
                })
                .forEach(label => {
                    const value = partyJson[label];
                    const mapping = tianGanMap[label];
                    
                    if (value && mapping) {
                        parties.push({
                            label: label,
                            value: value,
                            field: mapping.field
                        });
                    }
                });

            // 确保至少有甲方和乙方
            if (parties.length === 0) {
                parties.push(
                    { label: "甲方", value: "", field: "partA" },
                    { label: "乙方", value: "", field: "partB" }
                );
            } else if (parties.length === 1) {
                // 如果只有一方，补充另一方
                const hasJia = parties.some(p => p.label === "甲方");
                if (!hasJia) {
                    parties.unshift({ label: "甲方", value: "", field: "partA" });
                } else {
                    parties.push({ label: "乙方", value: "", field: "partB" });
                }
            }

            return parties;
        }

        // 将组件内部格式转换为JSON格式
        function convertPartiesToJson(contractParties) {
            const partyJson = {};
            
            contractParties.forEach(party => {
                if (party.value && party.value.trim()) {
                    partyJson[party.label] = party.value.trim();
                }
            });

            return Object.keys(partyJson).length > 0 ? JSON.stringify(partyJson) : '';
        }

        function testJsonToParties() {
            const input = document.getElementById('jsonInput').value;
            const resultDiv = document.getElementById('jsonResult');
            
            try {
                const partyJson = JSON.parse(input);
                const parties = convertJsonToParties(partyJson);
                resultDiv.innerHTML = `<span class="success">转换成功：</span>\n${JSON.stringify(parties, null, 2)}`;
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">转换失败：</span>\n${error.message}`;
            }
        }

        function testPartiesToJson() {
            const input = document.getElementById('partiesInput').value;
            const resultDiv = document.getElementById('partiesResult');
            
            try {
                const parties = JSON.parse(input);
                const jsonResult = convertPartiesToJson(parties);
                resultDiv.innerHTML = `<span class="success">转换成功：</span>\n${jsonResult}`;
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">转换失败：</span>\n${error.message}`;
            }
        }

        function testFullFlow() {
            const input = document.getElementById('fullTestInput').value;
            const resultDiv = document.getElementById('fullTestResult');
            
            try {
                // 步骤1：解析原始JSON
                const originalJson = JSON.parse(input);
                
                // 步骤2：转换为内部格式
                const parties = convertJsonToParties(originalJson);
                
                // 步骤3：转换回JSON格式
                const finalJson = convertPartiesToJson(parties);
                
                resultDiv.innerHTML = `<span class="success">完整流程测试成功：</span>
原始JSON: ${input}
内部格式: ${JSON.stringify(parties, null, 2)}
最终JSON: ${finalJson}

<span class="success">数据一致性检查：</span>
${JSON.stringify(originalJson) === finalJson ? '✓ 数据一致' : '✗ 数据不一致'}`;
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">测试失败：</span>\n${error.message}`;
            }
        }

        // 页面加载时设置默认值
        window.onload = function() {
            document.getElementById('jsonInput').value = '{"乙方":"邦道科技有限公司","甲方":"新耀能源科技有限公司","丙方":"浙江纽伦轴承有限公司"}';
            document.getElementById('partiesInput').value = '[{"label":"甲方","value":"新耀能源科技有限公司","field":"partA"},{"label":"乙方","value":"邦道科技有限公司","field":"partB"},{"label":"丙方","value":"浙江纽伦轴承有限公司","field":"partC"}]';
            document.getElementById('fullTestInput').value = '{"乙方":"邦道科技有限公司","甲方":"新耀能源科技有限公司","丙方":"浙江纽伦轴承有限公司"}';
        };
    </script>
</body>
</html>
