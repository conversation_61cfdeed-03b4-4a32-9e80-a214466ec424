<!-- 新增/编辑合同协议 -->
<template>
  <div class="app-container" v-loading="pageLoading">
    <h3>{{ type === "add" ? "新增" : "编辑" }}合同协议</h3>

    <!-- 基本信息 -->
    <el-card id="baseInfo">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>基本信息</span>
      </div>
      <DynamicForm
        ref="baseForm"
        :config="baseConfig"
        :params="baseParams"
        :defaultColSpan="24"
        labelPosition="right"
        labelWidth="150px"
      >
        <!-- 动态多方字段插槽 -->
        <template #contractParties>
          <div class="contract-parties">
            <div
              v-for="(party, index) in contractParties"
              :key="index"
              class="party-item"
            >
              <el-autocomplete
                v-model="party.value"
                :placeholder="`请输入${party.label}`"
                :fetch-suggestions="
                  (queryString, cb) => queryContractPartySearch(queryString, cb)
                "
                style="width: calc(100% - 40px); margin-right: 10px;"
                clearable
              >
                <template slot="prepend">{{ party.label }}</template>
              </el-autocomplete>
              <el-button
                v-if="index === 0 && contractParties.length < 10"
                @click="addContractParty"
                type="primary"
                icon="el-icon-plus"
                size="small"
                circle
              ></el-button>
              <el-button
                v-if="
                  contractParties.length > 2 &&
                    index === contractParties.length - 1
                "
                @click="removeContractParty(index)"
                type="danger"
                icon="el-icon-minus"
                size="small"
                circle
                style="margin-left: 5px;"
              ></el-button>
            </div>
          </div>
        </template>
      </DynamicForm>
    </el-card>

    <!-- 其他信息 -->
    <el-card id="otherInfo" class="mt20">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>其他信息</span>
      </div>
      <ContractOtherInfo
        ref="otherInfo"
        :contractTypeName="baseParams.contractTypeName"
        v-model="otherParams"
      />
    </el-card>

    <!-- 操作按钮 -->
    <div class="dialog-footer">
      <el-button @click.stop="goBack" size="medium" :loading="btnLoading">
        取 消
      </el-button>
      <el-button @click="submit(0)" size="medium" :loading="btnLoading"
        >保存</el-button
      >
      <el-button
        @click="submit(1)"
        type="primary"
        size="medium"
        :loading="btnLoading"
        >提交</el-button
      >
    </div>
  </div>
</template>

<script>
import { initParams } from "@/utils/buse.js";
import api from "@/api/infoArchive/contractAgreement/agreement.js";
import { queryTreeList } from "@/api/ledger/businessType.js";
import ContractOtherInfo from "./components/ContractOtherInfo.vue";

export default {
  name: "agreementAddPage",
  components: {
    ContractOtherInfo,
  },
  data() {
    return {
      contractId: "",
      btnLoading: false,
      pageLoading: false,
      type: "add",
      baseParams: {},
      otherParams: {},
      contractAgreementTypeOptions: [], // 合同协议类型字典
      agreementTypeOptions: [], // 协议类型字典
      businessTypeOptions: [], // 业务类型选项
      // 动态多方数据
      contractParties: [
        { label: "甲方", value: "", field: "partA" },
        { label: "乙方", value: "", field: "partB" },
      ],
      // 天干顺序用于命名
      tianGan: ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"],
    };
  },
  computed: {
    // 基本信息表单配置
    baseConfig() {
      return [
        {
          field: "omApplyNo",
          title: "运管申请单号",
          element: "page-autocomplete",
          props: {
            fetchMethod: (params) => {
              return this.queryOmApplyNoSearch(params, "omApplyNo");
            },
            optionValue: "omApplyNo",
          },
          attrs: {
            placeholder: "请输入或选择运管申请单号",
            clearable: true,
          },
          on: {
            select: this.handleOmApplyNoSelect,
          },
        },
        {
          field: "contractNo",
          title: "合同编码",
          element: "el-autocomplete",
          props: {
            fetchSuggestions: (queryString, cb) => {
              return this.queryContractCodeSearch(queryString, cb);
            },
            clearable: true,
            placeholder: "请输入或选择合同编码",
          },
          attrs: {
            maxlength: 100,
          },
        },
        {
          field: "contractTypeName",
          title: "合同&协议类型",
          element: "el-select",
          props: {
            options: this.contractAgreementTypeOptions,
            optionValue: "dictLabel",
            optionLabel: "dictLabel",
            clearable: true,
          },
          rules: [
            {
              required: true,
              message: "请选择合同&协议类型",
              trigger: "change",
            },
          ],
        },
        {
          field: "contractName",
          title: "合同&协议名称",
          element: "el-input",
          attrs: {
            maxlength: 100,
            showWordLimit: true,
          },
          rules: [
            {
              required: true,
              message: "请输入合同&协议名称",
              trigger: "change",
            },
          ],
        },
        {
          field: "businessType",
          title: "业务类型",
          element: "el-autocomplete",
          props: {
            fetchSuggestions: (queryString, cb) => {
              return this.queryBusinessTypeSearch(queryString, cb);
            },
            clearable: true,
          },
          attrs: {
            maxlength: 100,
          },
          rules: [
            {
              required: true,
              message: "请输入或选择业务类型",
              trigger: "change",
            },
          ],
        },
        {
          field: "docList",
          element: "file-upload",
          title: "附件/图片",
          props: {
            limit: 20,
            accept: ".jpg, .jpeg, .png, .xls, .xlsx, .pdf, .doc, .docx",
            textTip:
              "支持批量上传，上传格式为jpg、jpeg、png、xls、xlsx、pdf、doc、docx文件，单个文件200M以内",
          },
          rules: [
            {
              required: true,
              message: "请上传",
              trigger: "change",
            },
          ],
        },
        {
          field: "effectiveTime",
          title: "合同&协议生效时间",
          element: "el-date-picker",
          props: {
            type: "date",
            valueFormat: "yyyy-MM-dd",
          },
          rules: [
            {
              required: true,
              message: "请选择生效时间",
              trigger: "change",
            },
          ],
        },
        {
          field: "expireTime",
          title: "合同&协议失效时间",
          element: "el-date-picker",
          props: {
            type: "date",
            valueFormat: "yyyy-MM-dd",
          },
          rules: [
            {
              required: true,
              message: "请选择失效时间",
              trigger: "change",
            },
          ],
        },
        {
          field: "contractParties",
          title: "合同协议方",
          element: "slot",
          slotName: "contractParties",
          // rules: [
          //   {
          //     required: true,
          //     message: "请输入合同协议方",
          //     trigger: "change",
          //   },
          // ],
        },
        {
          field: "remark",
          title: "备注",
          element: "el-input",
          attrs: {
            type: "textarea",
            maxlength: 10000,
            rows: 5,
            showWordLimit: true,
            placeholder: "10000个字符以内",
          },
        },
      ];
    },
    // 其他信息表单配置（暂时留空）
    otherConfig() {
      return [];
    },
  },
  created() {
    this.contractId = this.$route.query.contractId;
    this.type = this.$route.query.type || "add";

    // 初始化表单参数
    this.baseParams = {
      ...initParams(this.baseConfig),
    };
    this.otherParams = {
      ...initParams(this.otherConfig),
    };

    // 获取字典数据
    this.getDicts("contract_type").then((response) => {
      this.contractAgreementTypeOptions = response.data;
    });

    // 获取协议类型字典
    this.getDicts("agreement_type").then((response) => {
      this.agreementTypeOptions = response.data;
    });

    // 获取业务类型数据
    this.getBusinessTypeOptions();

    // 如果是编辑模式，获取详情数据
    if (this.type === "edit" && this.contractId) {
      this.getDetail();
    }
  },
  methods: {
    // 获取业务类型选项
    getBusinessTypeOptions() {
      queryTreeList({ pageSize: 99999, pageNum: 1 }).then((res) => {
        this.businessTypeOptions = res.data;
      });
    },

    async queryOmApplyNoSearch(params, key) {
      const { searchText, ...rest } = params;
      const res = await api.queryOmApplyNoOptions({
        ...rest,
        [key]: searchText,
      });
      return {
        data: res.data,
        total: res.total,
      };
    },
    // 合同编码搜索
    queryContractCodeSearch(queryString, cb) {
      api
        .queryContractCodeOptions({
          name: queryString,
        })
        .then((res) => {
          const result = res.data?.map((x) => {
            return { value: x.contractCode || x };
          });
          cb(result);
        });
    },

    // 业务类型搜索
    queryBusinessTypeSearch(queryString, cb) {
      queryTreeList({
        pageSize: 99999,
        pageNum: 1,
      }).then((res) => {
        const options =
          res.data?.map((x) => {
            return { value: x.typeName };
          }) || [];
        const results = queryString
          ? options.filter((item) =>
              item.value.toLowerCase().includes(queryString.toLowerCase())
            )
          : options;

        cb(results);
      });
    },

    // 合同协议方搜索
    queryContractPartySearch(queryString, cb) {
      api
        .queryContractParty({
          name: queryString,
          pageNum: 1,
          pageSize: 10,
        })
        .then((res) => {
          const result = res.data?.map((x) => {
            return { value: x };
          });
          cb(result);
        });
    },

    // 处理运管申请单号选择后的自动填充
    handleOmApplyNoSelect(data) {
      if (!data.omApplyNo) return;

      // 根据运管申请单号查询基本信息并自动填充

      // 自动填充相关字段
      if (data.businessType) {
        this.baseParams.businessType = data.businessType;
      }
      if (data.contractName) {
        this.baseParams.contractName = data.contractName;
      }
      if (data.contractTypeName) {
        this.baseParams.contractTypeName = data.contractTypeName;
      }
      if (data.effectiveTime) {
        this.baseParams.effectiveTime = data.effectiveTime;
      }
      if (data.expireTime) {
        this.baseParams.expireTime = data.expireTime;
      }

      // 自动填充合同协议方信息
      this.parseAndSetContractParties(data);

      // 自动填充其他信息
      this.parseAndSetOtherParams(data);
    },

    // 解析并设置合同协议方信息
    parseAndSetContractParties(data) {
      console.log("解析合同协议方数据:", data);

      // 处理contractPartyJson格式的数据
      if (data.contractPartyJson) {
        try {
          const partyJson =
            typeof data.contractPartyJson === "string"
              ? JSON.parse(data.contractPartyJson)
              : data.contractPartyJson;

          console.log("解析到的contractPartyJson:", partyJson);
          this.contractParties = this.convertJsonToParties(partyJson);
          console.log("转换后的contractParties:", this.contractParties);
          return;
        } catch (error) {
          console.error("解析contractPartyJson失败:", error);
        }
      }

      // 兼容原有的partA、partB等字段格式
      if (data.partA || data.partB) {
        this.contractParties = [
          { label: "甲方", value: data.partA || "", field: "partA" },
          { label: "乙方", value: data.partB || "", field: "partB" },
        ];

        // 如果有更多方，继续添加
        for (let i = 2; i < 10; i++) {
          const fieldName = "part" + this.tianGan[i];
          if (data[fieldName]) {
            this.contractParties.push({
              label: this.tianGan[i] + "方",
              value: data[fieldName],
              field: fieldName,
            });
          }
        }

        console.log("使用兼容格式解析的contractParties:", this.contractParties);
      }
    },

    // 将JSON格式的协议方数据转换为组件内部格式
    convertJsonToParties(partyJson) {
      const parties = [];

      // 天干顺序映射
      const tianGanMap = {
        甲方: { index: 0, field: "partA" },
        乙方: { index: 1, field: "partB" },
        丙方: { index: 2, field: "partC" },
        丁方: { index: 3, field: "partD" },
        戊方: { index: 4, field: "partE" },
        己方: { index: 5, field: "partF" },
        庚方: { index: 6, field: "partG" },
        辛方: { index: 7, field: "partH" },
        壬方: { index: 8, field: "partI" },
        癸方: { index: 9, field: "partJ" },
      };

      // 按照天干顺序排序并转换
      Object.keys(partyJson)
        .sort((a, b) => {
          const indexA = tianGanMap[a]?.index ?? 999;
          const indexB = tianGanMap[b]?.index ?? 999;
          return indexA - indexB;
        })
        .forEach((label) => {
          const value = partyJson[label];
          const mapping = tianGanMap[label];

          if (value && mapping) {
            parties.push({
              label: label,
              value: value,
              field: mapping.field,
            });
          }
        });

      // 确保至少有甲方和乙方
      if (parties.length === 0) {
        parties.push(
          { label: "甲方", value: "", field: "partA" },
          { label: "乙方", value: "", field: "partB" }
        );
      } else if (parties.length === 1) {
        // 如果只有一方，补充另一方
        const hasJia = parties.some((p) => p.label === "甲方");
        if (!hasJia) {
          parties.unshift({ label: "甲方", value: "", field: "partA" });
        } else {
          parties.push({ label: "乙方", value: "", field: "partB" });
        }
      }

      return parties;
    },

    // 将组件内部格式转换为JSON格式
    convertPartiesToJson() {
      const partyJson = {};

      this.contractParties.forEach((party) => {
        if (party.value && party.value.trim()) {
          partyJson[party.label] = party.value.trim();
        }
      });

      return Object.keys(partyJson).length > 0 ? JSON.stringify(partyJson) : "";
    },

    // 解析并设置其他信息数据
    parseAndSetOtherParams(data) {
      console.log("解析其他信息数据:", data);

      const otherData = {};

      // 根据合同协议类型获取需要回显的字段
      const contractTypeName =
        data.contractTypeName || this.baseParams.contractTypeName;

      if (!contractTypeName) {
        console.warn("合同协议类型为空，无法确定其他信息字段");
        return;
      }

      // 基础表单字段映射
      const formFieldsMapping = {
        // 投建协议相关字段
        channelInfo: data.channelInfo,
        projectName: data.projectName,
        constructionCode: data.constructionCode,

        // 合同申请记录字段
        contractAmountRatio: data.contractAmountOrRatio,

        // 协议申请记录字段
        agreementType: data.agreementType,

        // 采购框架协议字段
        businessPolicy: data.businessPolicy,

        // 其他可能的字段
        rentalFee: data.rentalFee,
        changeReason: data.changeReason,
        changeDate: data.changeDate,
      };

      // 只添加有值的字段
      Object.keys(formFieldsMapping).forEach((key) => {
        const value = formFieldsMapping[key];
        if (value !== undefined && value !== null && value !== "") {
          otherData[key] = value;
        }
      });

      // 处理投建协议详情表格数据
      if (data.toujianxieyiList && Array.isArray(data.toujianxieyiList)) {
        otherData.toujianxieyiList = data.toujianxieyiList.map((item) => ({
          detailId: item.detailId,
          contractId: item.contractId,
          stationName: item.stationName || "",
          stationCode: item.stationCode || "",
          region: item.region || "",
          rentalFeeOrSplitRatio: item.rentalFeeOrSplitRatio || 0,
          meterOwner: item.meterOwner || "",
          billingMethod: item.billingMethod || "",
          billingStandard: item.billingStandard || 0,
          hasLateFee: item.hasLateFee || "N",
          revenueModel: item.revenueModel || "",
          pileCount: item.pileCount || 0,
          gunCount: item.gunCount || 0,
          createTime: item.createTime,
          updateTime: item.updateTime,
        }));
      }

      // 处理变更附件数据
      if (data.changeDocList && Array.isArray(data.changeDocList)) {
        otherData.changeDocList = data.changeDocList.map((doc) => ({
          name: doc.docName,
          docName: doc.docName,
          storePath: doc.storePath,
          url: doc.storePath,
          storeType: doc.storeType,
          docType: doc.docType,
          docClassify: doc.docClassify,
          directory: doc.directory,
          docId: doc.docId,
          businessType: doc.businessType,
          previewPath: doc.previewPath,
        }));
      }

      // 更新 otherParams
      this.otherParams = { ...this.otherParams, ...otherData };

      console.log("回显的其他信息数据:", this.otherParams);
    },

    // 添加合同协议方
    addContractParty() {
      if (this.contractParties.length < 10) {
        const index = this.contractParties.length;
        const label = this.tianGan[index] + "方";
        const field = "part" + this.tianGan[index];
        this.contractParties.push({
          label: label,
          value: "",
          field: field,
        });
      }
    },

    // 移除合同协议方
    removeContractParty(index) {
      if (this.contractParties.length > 2) {
        this.contractParties.splice(index, 1);
      }
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 获取详情数据（编辑模式）
    async getDetail() {
      if (!this.contractId) return;
      this.pageLoading = true;
      try {
        const res = await api.getContractDetail({
          contractId: this.contractId,
        });
        this.pageLoading = false;
        if (res?.code === "10000") {
          const data = res.data;
          // 回显基本信息
          this.baseParams = { ...this.baseParams, ...data };

          // 确保合同协议类型字段正确回显（使用字典标签）
          if (data.contractTypeName) {
            this.baseParams.contractTypeName = data.contractTypeName;
            console.log("编辑模式回显合同协议类型:", data.contractTypeName);
          }

          // 处理文件数据回显
          if (data.docList && Array.isArray(data.docList)) {
            this.baseParams.docList = data.docList.map((doc) => ({
              name: doc.docName,
              docName: doc.docName,
              storePath: doc.storePath,
              url: doc.storePath,
              storeType: doc.storeType,
              docType: doc.docType,
              docClassify: doc.docClassify,
              directory: doc.directory,
              docId: doc.docId,
            }));
          }

          // 回显合同协议方信息
          this.parseAndSetContractParties(data);

          // 回显其他信息（otherParams）
          this.parseAndSetOtherParams(data);
        }
      } catch (error) {
        this.pageLoading = false;
        console.error("获取详情失败:", error);
      }
    },

    // 提交保存
    async submit(submitFlag = 0) {
      console.log("提交数据:", this.baseParams, this.otherParams);

      // 表单验证
      const baseValid = await this.$refs.baseForm.validate().catch((error) => {
        console.error("基本信息验证失败:", error);
        this.$message.error("基本信息填写有误，请检查必填项");
        return false;
      });
      if (!baseValid) {
        return;
      }

      // 验证合同协议方
      const hasValidParty = this.contractParties.some(
        (party) => party.value && party.value.trim()
      );
      if (!hasValidParty) {
        this.$message.error("请至少填写一个合同协议方");
        return;
      }

      // 其他信息验证
      const otherValid = await this.$refs.otherInfo
        .validate()
        .catch((error) => {
          console.error("其他信息验证失败:", error);
          return false;
        });
      if (!otherValid) {
        this.$message.warning("其他信息验证失败，请检查表格数据");
        return;
      }

      // 验证日期逻辑
      if (this.baseParams.effectiveTime && this.baseParams.expireTime) {
        const effectiveDate = new Date(this.baseParams.effectiveTime);
        const expireDate = new Date(this.baseParams.expireTime);
        if (effectiveDate >= expireDate) {
          this.$message.error("生效时间必须早于失效时间");
          return;
        }
      }

      const actionText = submitFlag === 1 ? "提交" : "保存";
      this.$confirm(`是否确认${actionText}？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        // 组装提交数据
        const params = {
          ...this.baseParams,
          ...this.otherParams,
          submitFlag: submitFlag, // 0:保存 1:提交
        };

        // 验证合同协议类型字段
        if (!params.contractTypeName) {
          this.$message.error("请选择合同协议类型");
          return;
        }

        console.log("提交的合同协议类型:", params.contractTypeName);

        // 添加合同协议方数据 - 同时支持新旧格式
        // 新格式：contractPartyJson
        const contractPartyJson = this.convertPartiesToJson();
        if (contractPartyJson) {
          params.contractPartyJson = contractPartyJson;
          console.log("提交的contractPartyJson:", contractPartyJson);
        }

        // 兼容旧格式：partA、partB等字段
        this.contractParties.forEach((party) => {
          if (party.value) {
            params[party.field] = party.value;
          }
        });

        console.log("提交的合同协议方数据:", {
          contractPartyJson: params.contractPartyJson,
          parties: this.contractParties,
        });

        // 处理其他信息的特殊字段格式
        this.processOtherParamsForSubmit(params);

        // 处理文件上传数据格式
        if (params.docList && Array.isArray(params.docList)) {
          params.docList = params.docList.map((file) => ({
            docName: file.name || file.docName,
            storePath: file.storePath || file.url,
            storeType: file.storeType || "0", // 默认OSS存储
            docType: "1", // 业务照片
            docClassify: "contract", // 合同分类
            directory: "contract", // 合同目录
            delFlag: "0", // 正常状态
            businessType: params.businessType || "", // 业务类型
            relaBizId: this.contractId || 0, // 关联业务ID
          }));
        }

        // 处理投建协议数据格式
        if (params.toujianxieyiList && Array.isArray(params.toujianxieyiList)) {
          params.toujianxieyiList = params.toujianxieyiList.map((item) => ({
            ...item,
            contractId: this.contractId || 0,
            // 确保数字字段的精度
            rentalFeeOrSplitRatio: item.rentalFeeOrSplitRatio
              ? parseFloat(item.rentalFeeOrSplitRatio).toFixed(1)
              : 0.0,
            billingStandard: item.billingStandard
              ? parseFloat(item.billingStandard).toFixed(4)
              : 0.0,
            pileCount: parseInt(item.pileCount) || 0,
            gunCount: parseInt(item.gunCount) || 0,
            // 确保布尔值字段格式正确
            hasLateFee:
              item.hasLateFee === "Y" || item.hasLateFee === true ? "Y" : "N",
          }));
        }

        // 处理日期格式
        if (params.effectiveTime) {
          params.effectiveTime = this.formatDate(params.effectiveTime);
        }
        if (params.expireTime) {
          params.expireTime = this.formatDate(params.expireTime);
        }

        // 如果是编辑模式，添加contractId
        if (this.type === "edit" && this.contractId) {
          params.contractId = this.contractId;
        }

        this.btnLoading = true;
        try {
          const res = await api.saveContract(params);
          this.btnLoading = false;
          if (res?.code === "10000") {
            this.$message.success(`${actionText}成功`);
            this.goBack();
          } else {
            this.$message.error(res?.message || `${actionText}失败`);
          }
        } catch (error) {
          this.btnLoading = false;
          this.$message.error(`${actionText}失败: ${error.message || error}`);
          console.error(`${actionText}失败:`, error);
        }
      });
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return "";

      // 如果已经是字符串格式，直接返回
      if (typeof date === "string" && date.match(/^\d{4}-\d{2}-\d{2}$/)) {
        return date;
      }

      // 如果是Date对象，格式化为YYYY-MM-DD
      if (date instanceof Date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        return `${year}-${month}-${day}`;
      }

      return date;
    },

    // 处理其他信息的特殊字段格式
    processOtherParamsForSubmit(params) {
      // 处理日期字段格式
      if (params.changeDate) {
        params.changeDate = this.formatDate(params.changeDate);
      }

      // 处理变更附件数据格式
      if (params.changeDocList && Array.isArray(params.changeDocList)) {
        params.changeDocList = params.changeDocList.map((file) => ({
          docName: file.name || file.docName,
          storePath: file.storePath || file.url,
          storeType: file.storeType || "0", // 默认OSS存储
          docType: file.docType || "1", // 业务照片
          docClassify: file.docClassify || "contract", // 合同分类
          directory: file.directory || "contract", // 合同目录
          delFlag: "0", // 正常状态
          businessType: params.businessType || "", // 业务类型
          relaBizId: this.contractId || 0, // 关联业务ID
          docId: file.docId,
          businessNo: file.businessNo,
          projectId: file.projectId,
          taskDefKey: file.taskDefKey,
          uploader: file.uploader,
          uploaderName: file.uploaderName,
          uploadTime: file.uploadTime,
          remark: file.remark,
          previewPath: file.previewPath,
          completionAuditReason: file.completionAuditReason,
        }));
      }

      // 确保数字字段的正确格式
      if (params.rentalFee !== undefined && params.rentalFee !== null) {
        params.rentalFee = parseFloat(params.rentalFee) || 0;
      }

      console.log("处理后的其他信息数据:", {
        otherParams: this.otherParams,
        processedFields: {
          changeDate: params.changeDate,
          changeDocList: params.changeDocList,
          rentalFee: params.rentalFee,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
.contract-parties {
  .party-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
