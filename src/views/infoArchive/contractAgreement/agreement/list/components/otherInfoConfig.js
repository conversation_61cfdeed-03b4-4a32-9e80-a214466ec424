/**
 * 合同协议其他信息配置
 * 统一的数据结构，支持编辑和展示两种场景
 */

// 字典数据映射
export const DICT_TYPES = {
  AREA: "area_config", // 区域配置
  ELECTRICITY_BILLING_METHOD: "billing_method", // 电费计费方式
  HAS_LATE_FEE: "sys_yes_no", // 是否有滞纳金
  REVENUE_MODE: "revenue_model", // 收益模式
  AGREEMENT_TYPE: "contract_type", // 协议类型
};

// 投建协议表格字段配置
export const INVESTMENT_TABLE_COLUMNS = [
  { type: "checkbox", width: 60, fixed: "left" },
  {
    title: "场所名称",
    field: "stationName",
    width: 120,
    isEdit: true,
    element: "el-input",
    props: {
      placeholder: "请输入场所名称",
      maxlength: 100,
    },
    rules: [],
  },
  {
    title: "场所编码",
    field: "stationCode",
    width: 120,
    isEdit: true,
    element: "el-input",
    props: {
      placeholder: "请输入场所编码",
      maxlength: 100,
    },
    rules: [],
  },
  {
    title: "区域",
    field: "region",
    width: 150,
    isEdit: true,
    element: "el-autocomplete",
    props: {
      placeholder: "请选择或输入区域",
      fetchSuggestions: null, // 将在组件中动态设置
    },
    rules: [],
  },
  {
    title: "场地租赁费/分成比例(%)",
    field: "rentalFeeOrSplitRatio",
    width: 180,
    isEdit: true,
    element: "el-input-number",
    props: {
      placeholder: "请输入比例",
      precision: 1,
      min: 0,
      max: 100,
      controlsPosition: "right",
    },
    rules: [],
  },
  {
    title: "电表户号归属方",
    field: "meterOwner",
    width: 150,
    isEdit: true,
    element: "el-input",
    props: {
      placeholder: "请输入归属方",
      maxlength: 100,
    },
    rules: [],
  },
  {
    title: "电费计费方式",
    field: "billingMethod",
    width: 150,
    isEdit: true,
    element: "el-select",
    props: {
      placeholder: "请选择计费方式",
      options: [], // 将在组件中动态设置
      optionLabel: "dictLabel",
      optionValue: "dictValue",
      filterable: true,
    },
    rules: [],
  },
  {
    title: "电费计费标准(元/kWh)",
    field: "billingStandard",
    width: 180,
    isEdit: true,
    element: "el-input-number",
    props: {
      placeholder: "请输入标准",
      precision: 4,
      min: 0,
      controlsPosition: "right",
    },
    rules: [],
  },
  {
    title: "是否有滞纳金",
    field: "hasLateFee",
    width: 120,
    isEdit: true,
    element: "el-select",
    props: {
      placeholder: "请选择",
      options: [], // 将在组件中动态设置
      optionLabel: "dictLabel",
      optionValue: "dictValue",
    },
    rules: [],
  },
  {
    title: "收益模式",
    field: "revenueMode",
    width: 120,
    isEdit: true,
    element: "el-select",
    props: {
      placeholder: "请选择收益模式",
      options: [], // 将在组件中动态设置
      optionLabel: "dictLabel",
      optionValue: "dictValue",
    },
    rules: [],
  },
  {
    title: "直流/交流桩数",
    field: "pileCount",
    width: 150,
    isEdit: true,
    element: "el-input-number",
    props: {
      placeholder: "请输入桩数",
      precision: 0,
      min: 0,
      max: 9999,
      controlsPosition: "right",
    },
    rules: [],
  },
  {
    title: "枪数",
    field: "gunCount",
    width: 120,
    isEdit: true,
    element: "el-input-number",
    props: {
      placeholder: "请输入枪数",
      precision: 0,
      min: 0,
      max: 9999,
      controlsPosition: "right",
    },
    rules: [],
  },
];

// 合同协议类型配置映射
export const CONTRACT_TYPE_CONFIG = {
  // 投建协议申请记录-充电
  "投建协议申请记录-充电": {
    name: "投建协议申请记录-充电",
    formFields: [
      {
        field: "channelInfo",
        title: "渠道信息",
        element: "el-input",
        props: {
          placeholder: "请输入渠道信息，100个字符以内",
          maxlength: 100,
          showWordLimit: true,
        },
        rules: [],
      },
      {
        field: "projectName",
        title: "项目名称",
        element: "el-input",
        props: {
          placeholder: "请输入项目名称，100个字符以内",
          maxlength: 100,
          showWordLimit: true,
        },
        rules: [],
      },
      {
        field: "constructionCode",
        title: "在建工程编码",
        element: "el-input",
        props: {
          placeholder: "请输入在建工程编码，多个用逗号分隔",
          maxlength: 100,
          showWordLimit: true,
        },
        rules: [],
      },
    ],
    tableFields: [
      {
        field: "toujianxieyiList",
        title: "投建协议详情",
        element: "table",
        tableType: "investment",
        description: "场所名称、场所编码、区域等信息",
      },
    ],
    displayFields: [
      { title: "渠道信息", value: "channelInfo" },
      { title: "项目名称", value: "projectName" },
      { title: "在建工程编码", value: "constructionCode" },
    ],
  },

  // 投建协议申请记录-储能
  "投建协议申请记录-储能": {
    name: "投建协议申请记录-储能",
    formFields: [
      {
        field: "channelInfo",
        title: "渠道信息",
        element: "el-input",
        props: {
          placeholder: "请输入渠道信息，100个字符以内",
          maxlength: 100,
          showWordLimit: true,
        },
        rules: [],
      },
      {
        field: "projectName",
        title: "项目名称",
        element: "el-input",
        props: {
          placeholder: "请输入项目名称，100个字符以内",
          maxlength: 100,
          showWordLimit: true,
        },
        rules: [],
      },
      {
        field: "constructionCode",
        title: "在建工程编码",
        element: "el-input",
        props: {
          placeholder: "请输入在建工程编码，多个用逗号分隔",
          maxlength: 100,
          showWordLimit: true,
        },
        rules: [],
      },
    ],
    tableFields: [
      {
        field: "toujianxieyiList",
        title: "投建协议详情",
        element: "table",
        tableType: "investment",
        description: "场所名称、场所编码、区域等信息",
      },
    ],
    displayFields: [
      { title: "渠道信息", value: "channelInfo" },
      { title: "项目名称", value: "projectName" },
      { title: "在建工程编码", value: "constructionCode" },
    ],
  },

  // 合同申请记录
  合同申请记录: {
    name: "合同申请记录",
    formFields: [
      {
        field: "contractAmountRatio",
        title: "合同金额/比例",
        element: "el-input",
        props: {
          placeholder: "请输入合同金额或比例，100个字符以内",
          maxlength: 100,
          showWordLimit: true,
        },
        rules: [],
      },
    ],
    tableFields: [],
    displayFields: [{ title: "合同金额/比例", value: "contractAmountRatio" }],
  },

  // 协议申请记录
  协议申请记录: {
    name: "协议申请记录",
    formFields: [
      {
        field: "agreementType",
        title: "协议类型",
        element: "el-select",
        props: {
          placeholder: "请选择协议类型",
          options: [], // 将在组件中动态设置
          optionLabel: "dictLabel",
          optionValue: "dictValue",
          clearable: true,
        },
        rules: [],
      },
    ],
    tableFields: [],
    displayFields: [{ title: "协议类型", value: "agreementType" }],
  },

  // 采购框架协议申请
  采购框架协议申请: {
    name: "采购框架协议申请",
    formFields: [
      {
        field: "businessPolicy",
        title: "商务政策(金额/分润比例)",
        element: "el-input",
        props: {
          placeholder: "请输入商务政策，100个字符以内",
          maxlength: 100,
          showWordLimit: true,
        },
        rules: [],
      },
    ],
    tableFields: [],
    displayFields: [
      { title: "商务政策(金额/分润比例)", value: "businessPolicy" },
    ],
  },
};

// 数字ID到字典标签的映射（兼容性支持）
const ID_TO_LABEL_MAP = {
  "1": "投建协议申请记录-充电",
  "2": "投建协议申请记录-储能",
  "3": "合同申请记录",
  "4": "协议申请记录",
  "5": "采购框架协议申请",
};

/**
 * 根据合同协议类型获取配置
 * @param {string} contractType - 合同协议类型（可以是数字ID或字典标签）
 * @returns {Object} 配置对象
 */
export function getConfigByType(contractType) {
  if (!contractType) {
    return {
      name: "未知类型",
      formFields: [],
      tableFields: [],
      displayFields: [],
    };
  }

  // 首先尝试直接匹配字典标签
  let config = CONTRACT_TYPE_CONFIG[contractType];

  // 如果没有找到，尝试通过数字ID映射
  if (!config && ID_TO_LABEL_MAP[contractType]) {
    const labelName = ID_TO_LABEL_MAP[contractType];
    config = CONTRACT_TYPE_CONFIG[labelName];
  }

  return (
    config || {
      name: "未知类型",
      formFields: [],
      tableFields: [],
      displayFields: [],
    }
  );
}

/**
 * 获取表格列配置
 * @param {string} tableType - 表格类型
 * @returns {Array} 列配置数组
 */
export function getTableColumns(tableType) {
  switch (tableType) {
    case "investment":
      return INVESTMENT_TABLE_COLUMNS;
    default:
      return [];
  }
}
