<template>
  <el-autocomplete
    ref="autocomplete"
    v-model="selectedValue"
    :fetch-suggestions="querySearch"
    v-auto-complete-loadmore="loadMore"
    v-bind="$attrs"
    @select="handleSelect"
    v-on="$listeners"
  >
  </el-autocomplete>
</template>

<script>
export default {
  name: "AutoCompletePagination",
  props: {
    value: [String, Number],
    fetchMethod: {
      type: Function,
      required: true,
    },
    optionValue: {
      type: String,
      default: "value",
    },

    pageSize: {
      type: Number,
      default: 10,
    },
    debounce: {
      type: Number,
      default: 300,
    },
  },
  data() {
    return {
      selectedValue: "",
      options: [],
      currentPage: 1,
      total: 0,
      searchText: "",
      loading: false,
      hasMore: true,
      timer: null,
    };
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        this.selectedValue = newVal;
      },
    },
  },
  mounted() {
    // this.loadData();
  },
  methods: {
    async querySearch(queryString, cb) {
      clearTimeout(this.timer);

      this.timer = setTimeout(async () => {
        if (this.searchText !== queryString) {
          this.resetPagination();
          this.searchText = queryString;
        }
        await this.loadData();
        console.log(this.options);
        cb(
          this.options.map((item) => {
            return {
              ...item,
              value: item[this.optionValue],
            };
          })
        );
      }, this.debounce);
    },

    async loadData() {
      if (this.loading || !this.hasMore) return;

      this.loading = true;
      try {
        const params = {
          pageNum: this.currentPage,
          pageSize: this.pageSize,
          searchText: this.searchText,
        };

        const res = await this.fetchMethod(params);
        this.options = [...this.options, ...res.data];
        this.total = res.total;
        this.hasMore = this.options.length < this.total;
        this.currentPage++;
        this.$refs.autocomplete?.suggestions.push(
          ...res.data?.map((item) => ({
            ...item,
            value: item[this.optionValue],
          }))
        );
        console.log(this.$refs.autocomplete);
      } finally {
        this.loading = false;
      }
    },

    handleSelect(item) {
      console.log("select", item);
      this.$emit("input", item.value);
      this.$emit("change", item.value, { options: this.options });
    },

    resetPagination() {
      this.currentPage = 1;
      this.options = [];
      this.hasMore = true;
    },
    loadMore() {
      if (!this.loading) {
        this.loadData();
      }
    },
  },
  directives: {
    "auto-complete-loadmore": {
      bind(el, binding) {
        const WRAPPER = el.querySelector(".el-autocomplete-suggestion__wrap");
        WRAPPER?.addEventListener("scroll", function() {
          const condition =
            this.scrollHeight - this.scrollTop <= this.clientHeight + 1;
          if (condition) {
            binding.value();
          }
        });
      },
    },
  },
};
</script>
