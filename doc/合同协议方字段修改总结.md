# 合同协议方字段contractPartyJson适配修改总结

## 修改概述

根据后端接口要求，合同协议方字段需要支持新的数据格式：`contractPartyJson`。该字段为JSON字符串格式，包含各方信息，且顺序不一定按照天干顺序返回。

## 主要修改内容

### 1. 新增数据处理方法

在 `src/views/infoArchive/contractAgreement/agreement/list/add.vue` 中新增了以下方法：

- `parseAndSetContractParties(data)` - 解析并设置合同协议方信息
- `convertJsonToParties(partyJson)` - 将JSON格式转换为组件内部格式  
- `convertPartiesToJson()` - 将组件内部格式转换为JSON格式

### 2. 修改数据回显逻辑

- 修改 `handleOmApplyNoSelect` 方法，使用新的解析方法
- 修改 `getDetail` 方法中的数据回显逻辑，优先使用 `contractPartyJson` 格式

### 3. 修改数据提交逻辑

在 `submit` 方法中：
- 生成 `contractPartyJson` 字段用于新格式
- 保留原有的 `partA`、`partB` 等字段用于兼容性
- 添加调试日志便于问题排查

## 核心特性

### 1. 双向兼容性
- **数据接收**：优先解析 `contractPartyJson`，如果不存在则使用原有的 `partA`、`partB` 等字段
- **数据提交**：同时发送新格式 `contractPartyJson` 和旧格式字段，确保后端兼容

### 2. 顺序处理
- 后端返回的JSON对象可能不按天干顺序
- 前端自动按照甲乙丙丁戊己庚辛壬癸的顺序重新排列
- 确保UI显示的一致性

### 3. 数据验证
- 确保至少有甲方和乙方两个基本方
- 支持最多10方（甲乙丙丁戊己庚辛壬癸）
- 如果只有一方，会自动补充另一方的空字段

### 4. 错误处理
- JSON解析失败时会降级到兼容格式
- 添加详细的控制台日志用于调试
- 保证在任何情况下都不会导致页面崩溃

## 数据格式示例

### 后端接口格式
```json
{
  "contractPartyJson": "{\"乙方\":\"邦道科技有限公司\",\"甲方\":\"新耀能源科技有限公司\",\"丙方\":\"浙江纽伦轴承有限公司\"}"
}
```

### 前端内部格式
```javascript
[
  { label: "甲方", value: "新耀能源科技有限公司", field: "partA" },
  { label: "乙方", value: "邦道科技有限公司", field: "partB" },
  { label: "丙方", value: "浙江纽伦轴承有限公司", field: "partC" }
]
```

### 提交数据格式
```json
{
  "contractPartyJson": "{\"甲方\":\"新耀能源科技有限公司\",\"乙方\":\"邦道科技有限公司\",\"丙方\":\"浙江纽伦轴承有限公司\"}",
  "partA": "新耀能源科技有限公司",
  "partB": "邦道科技有限公司", 
  "partC": "浙江纽伦轴承有限公司"
}
```

## 测试验证

### 测试文件
- `src/views/infoArchive/contractAgreement/agreement/list/test-contract-party.html` - 功能测试页面

### 测试内容
1. JSON格式转换为内部格式
2. 内部格式转换为JSON
3. 完整流程测试和数据一致性检查

## 使用场景

1. **新增合同协议**：用户手动输入各方信息，提交时生成 `contractPartyJson`
2. **编辑合同协议**：从后端获取 `contractPartyJson`，解析后回显到表单
3. **运管申请单号联动**：选择运管申请单号后自动填充协议方信息
4. **数据迁移**：支持从旧格式平滑迁移到新格式

## 注意事项

1. 确保后端接口能够正确处理 `contractPartyJson` 字段
2. 在生产环境中可以移除调试日志
3. 建议在后端也实现相应的数据格式兼容性处理
4. 测试时注意验证各种边界情况（空数据、单方、多方等）

## 文档说明

- `doc/合同协议方字段contractPartyJson适配说明.md` - 详细技术文档
- `doc/合同协议方字段修改总结.md` - 本总结文档

## 修改完成状态

✅ 数据解析方法实现  
✅ 数据转换方法实现  
✅ 数据回显逻辑修改  
✅ 数据提交逻辑修改  
✅ 兼容性处理  
✅ 错误处理  
✅ 测试页面创建  
✅ 技术文档编写
