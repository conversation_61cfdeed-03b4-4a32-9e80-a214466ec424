# 合同协议方字段contractPartyJson适配说明

## 概述

根据后端接口要求，合同协议方字段需要支持新的数据格式：`contractPartyJson`。该字段为JSON字符串格式，包含各方信息，且顺序不一定按照天干顺序返回。

## 数据格式说明

### 后端接口格式
```javascript
// 传参格式
contractPartyJson: '{"乙方":"邦道科技有限公司","甲方":"新耀能源科技有限公司","丙方":"浙江纽伦轴承有限公司"}'

// 解析后的对象
{
  "乙方": "邦道科技有限公司",
  "甲方": "新耀能源科技有限公司", 
  "丙方": "浙江纽伦轴承有限公司"
}
```

### 前端内部格式
```javascript
[
  { label: "甲方", value: "新耀能源科技有限公司", field: "partA" },
  { label: "乙方", value: "邦道科技有限公司", field: "partB" },
  { label: "丙方", value: "浙江纽伦轴承有限公司", field: "partC" }
]
```

## 核心功能实现

### 1. 数据解析方法 `parseAndSetContractParties`

```javascript
parseAndSetContractParties(data) {
  console.log("解析合同协议方数据:", data);
  
  // 优先处理contractPartyJson格式
  if (data.contractPartyJson) {
    try {
      const partyJson = typeof data.contractPartyJson === "string"
        ? JSON.parse(data.contractPartyJson)
        : data.contractPartyJson;

      this.contractParties = this.convertJsonToParties(partyJson);
      return;
    } catch (error) {
      console.error("解析contractPartyJson失败:", error);
    }
  }

  // 兼容原有partA、partB格式
  if (data.partA || data.partB) {
    // ... 兼容逻辑
  }
}
```

### 2. JSON转内部格式 `convertJsonToParties`

```javascript
convertJsonToParties(partyJson) {
  const parties = [];
  
  // 天干顺序映射
  const tianGanMap = {
    "甲方": { index: 0, field: "partA" },
    "乙方": { index: 1, field: "partB" },
    "丙方": { index: 2, field: "partC" },
    // ... 其他方
  };

  // 按天干顺序排序并转换
  Object.keys(partyJson)
    .sort((a, b) => {
      const indexA = tianGanMap[a]?.index ?? 999;
      const indexB = tianGanMap[b]?.index ?? 999;
      return indexA - indexB;
    })
    .forEach(label => {
      const value = partyJson[label];
      const mapping = tianGanMap[label];
      
      if (value && mapping) {
        parties.push({
          label: label,
          value: value,
          field: mapping.field
        });
      }
    });

  // 确保至少有甲方和乙方
  if (parties.length === 0) {
    parties.push(
      { label: "甲方", value: "", field: "partA" },
      { label: "乙方", value: "", field: "partB" }
    );
  }

  return parties;
}
```

### 3. 内部格式转JSON `convertPartiesToJson`

```javascript
convertPartiesToJson() {
  const partyJson = {};
  
  this.contractParties.forEach(party => {
    if (party.value && party.value.trim()) {
      partyJson[party.label] = party.value.trim();
    }
  });

  return Object.keys(partyJson).length > 0 ? JSON.stringify(partyJson) : '';
}
```

## 数据提交处理

### 提交时同时支持新旧格式

```javascript
// 新格式：contractPartyJson
const contractPartyJson = this.convertPartiesToJson();
if (contractPartyJson) {
  params.contractPartyJson = contractPartyJson;
}

// 兼容旧格式：partA、partB等字段
this.contractParties.forEach(party => {
  if (party.value) {
    params[party.field] = party.value;
  }
});
```

## 特性说明

### 1. 顺序处理
- 后端返回的JSON对象可能不按天干顺序
- 前端会自动按照甲乙丙丁戊己庚辛壬癸的顺序重新排列
- 确保UI显示的一致性

### 2. 兼容性
- 同时支持新的`contractPartyJson`格式和原有的`partA`、`partB`等字段格式
- 数据回显时优先使用`contractPartyJson`，如果不存在则使用原有格式
- 数据提交时同时发送两种格式，确保后端兼容性

### 3. 数据验证
- 确保至少有甲方和乙方两个基本方
- 如果只有一方，会自动补充另一方的空字段
- 支持最多10方（甲乙丙丁戊己庚辛壬癸）

### 4. 错误处理
- JSON解析失败时会降级到兼容格式
- 添加详细的控制台日志用于调试
- 保证在任何情况下都不会导致页面崩溃

## 测试验证

提供了测试页面 `test-contract-party.html` 用于验证：
1. JSON格式转换为内部格式
2. 内部格式转换为JSON
3. 完整流程测试和数据一致性检查

## 使用场景

1. **新增合同协议**：用户手动输入各方信息，提交时生成`contractPartyJson`
2. **编辑合同协议**：从后端获取`contractPartyJson`，解析后回显到表单
3. **运管申请单号联动**：选择运管申请单号后自动填充协议方信息
4. **数据迁移**：支持从旧格式平滑迁移到新格式

## 注意事项

1. 确保后端接口能够正确处理`contractPartyJson`字段
2. 在生产环境中可以移除调试日志
3. 建议在后端也实现相应的数据格式兼容性处理
4. 测试时注意验证各种边界情况（空数据、单方、多方等）
